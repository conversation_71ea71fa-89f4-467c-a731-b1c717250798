import { memo, useCallback, useState } from "react";
import { Input } from "./TextInput";
import { Button } from "./Buttons";
import { handleKeyDown } from "../function";

interface AddTodoFormProps {
  newTitle: string;
  setNewTitle: (title: string) => void;
  onAdd: () => void;
  isLoading?: boolean;
}

export const AddTodoForm = memo(function AddTodoForm({
  newTitle,
  setNewTitle,
  onAdd,
  isLoading = false,
}: AddTodoFormProps) {
  const [error, setError] = useState("");

  const validateAndAdd = useCallback(() => {
    const trimmedTitle = newTitle.trim();

    if (!trimmedTitle) {
      setError("Todo title cannot be empty");
      return;
    }

    if (trimmedTitle.length < 3) {
      setError("Todo title must be at least 3 characters");
      return;
    }

    setError("");
    onAdd();
  }, [newTitle, onAdd]);

  const handleTitleChange = useCallback(
    (title: string) => {
      setNewTitle(title);
      if (error) setError("");
    },
    [setNewTitle, error]
  );

  const isDisabled = isLoading;

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      validateAndAdd();
    },
    [validateAndAdd]
  );

  return (
    <form
      className="flex h-fit w-1/2 gap-2 items-start p-2 rounded-lg"
      onSubmit={handleSubmit}
    >
      <Input
        title={newTitle}
        setTitle={handleTitleChange}
        placeholder="minimum 3 characters"
        onKeyDown={(e) => handleKeyDown(e, validateAndAdd)}
        error={error}
        maxLength={100}
        disabled={isLoading}
      />
      <Button
        className="font-bold !text-xl w-24 mt-1"
        type="submit"
        variant="secondary"
        disabled={isDisabled}
      >
        ADD
      </Button>
    </form>
  );
});
