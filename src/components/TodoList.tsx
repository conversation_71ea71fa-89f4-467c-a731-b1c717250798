import type { Todo } from "../type";
import { Todo as TodoTask } from "./Todo";
import { Button } from "./Buttons";
import { memo } from "react";

interface TodoListProps {
  todos: Todo[];
  editId: number | null;
  editTitle: string;
  setEditTitle: (title: string) => void;
  onToggle: (id: number) => void;
  onDelete: (id: number) => void;
  onStartEdit: (todo: Todo) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  showEditControls?: boolean;
}

export const TodoList = memo(function TodoList({
  todos,
  editId,
  editTitle,
  setEditTitle,
  onToggle,
  onDelete,
  onStartEdit,
  onSaveEdit,
  onCancelEdit,
  showEditControls = true,
}: Readonly<TodoListProps>) {
  if (todos.length === 0) {
    return <p className="text-center text-gray-500 py-8">No todos yet</p>;
  }

  return (
    <ul aria-label="Todo items">
      {todos
        .slice()
        .reverse()
        .map((todo) => (
          <div
            key={todo.id}
            className="h-20 flex justify-between items-start gap-2 p-2 rounded hover:bg-gray-100 hover:rounded-2xl"
          >
            <TodoTask
              isComplete={todo.completed}
              title={todo.title}
              editTitle={editTitle}
              setEditTitle={setEditTitle}
              handleComplete={() => onToggle(todo.id)}
              id={todo.id}
              editId={editId}
            />
            <div className="flex" aria-label={`Actions for ${todo.title}`}>
              {showEditControls && editId === todo.id ? (
                <div className="flex flex-col">
                  <Button
                    variant="secondary"
                    onClick={onSaveEdit}
                    aria-label="Save changes"
                  >
                    Save
                  </Button>
                  <Button
                    variant="secondary-outline"
                    onClick={onCancelEdit}
                    aria-label="Cancel editing"
                  >
                    Cancel
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col">
                  {showEditControls && (
                    <Button
                      onClick={() => onStartEdit(todo)}
                      aria-label={`Edit ${todo.title}`}
                    >
                      Edit
                    </Button>
                  )}
                  <Button
                    variant="primary-outline"
                    onClick={() => onDelete(todo.id)}
                    aria-label={`Delete ${todo.title}`}
                  >
                    Delete
                  </Button>
                </div>
              )}
            </div>
          </div>
        ))}
    </ul>
  );
});
