import { useEffect, useReducer, useState, useCallback } from "react";
import { todoReducer } from "../reducer";
import { fetchTodos, deleteTodo, updateTodo, addTodo } from "../api";
import type { Todo } from "../type";
import { useEditState } from "./useEditState";

export function useTodos() {
  const [todos, dispatch] = useReducer(todoReducer, []);
  const [loading, setLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { cancelEdit } = useEditState();

  useEffect(() => {
    const loadTodos = async () => {
      try {
        const data = await fetchTodos();
        dispatch({ type: "SET_TODOS", payload: data });
      } catch (err) {
        console.error("Error fetching todos:", err);
        setError("Failed to fetch todos");
      } finally {
        setLoading(false);
      }
    };
    loadTodos();
  }, []);

  const addNewTodo = useCallback(async (title: string) => {
    if (!title.trim()) return false;
    setIsUpdating(true);
    setError(null);

    const newTodo: Omit<Todo, "id"> = {
      userId: 1,
      title: title.trim(),
      completed: false,
    };

    try {
      const created = await addTodo(newTodo);
      dispatch({ type: "ADD_TODO", payload: created });
      return true;
    } catch (err) {
      console.error("Error adding todo:", err);
      setError("Failed to add todo");
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, []);

  const toggleTodo = useCallback(
    async (id: number) => {
      const todoToUpdate = todos.find((t) => t.id === id);
      if (!todoToUpdate) return false;

      const updatedTodo = {
        ...todoToUpdate,
        completed: !todoToUpdate.completed,
      };

      try {
        dispatch({ type: "TOGGLE_TODO", payload: id });
        await updateTodo(updatedTodo);
        return true;
      } catch (err) {
        dispatch({ type: "TOGGLE_TODO", payload: id });
        console.error("Error toggling todo:", err);
        setError("Failed to update todo");
        return false;
      }
    },
    [todos]
  );

  const removeTodo = useCallback(
    async (id: number) => {
      const todoToDelete = todos.find((t) => t.id === id);
      if (!todoToDelete) return false;

      dispatch({ type: "DELETE_TODO", payload: id });
      setIsUpdating(true);

      try {
        await deleteTodo(id);
        return true;
      } catch (err) {
        dispatch({ type: "ADD_TODO", payload: todoToDelete });
        console.error("Error deleting todo:", err);
        setError("Failed to delete todo");
        return false;
      } finally {
        setIsUpdating(false);
      }
    },
    [todos]
  );

  const updateTodoItem = useCallback(
    async (id: number, title: string) => {
      const todoToUpdate = todos.find((t) => t.id === id);
      if (!todoToUpdate || todoToUpdate.title.trim() === title.trim()) {
        cancelEdit();
        return false;
      }

      const updated: Todo = {
        ...todoToUpdate,
        title: title.trim(),
      };

      dispatch({ type: "UPDATE_TODO", payload: updated });
      setIsUpdating(true);

      try {
        const result = await updateTodo(updated);
        dispatch({ type: "UPDATE_TODO", payload: result });
        return true;
      } catch (err) {
        dispatch({ type: "UPDATE_TODO", payload: todoToUpdate });
        console.error("Error updating todo:", err);
        setError("Failed to update todo");
        return false;
      } finally {
        setIsUpdating(false);
      }
    },
    [todos, cancelEdit]
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    todos,
    loading,
    error,
    addNewTodo,
    toggleTodo,
    removeTodo,
    updateTodoItem,
    clearError,
    isUpdating,
  };
}
